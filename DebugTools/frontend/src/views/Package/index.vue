<template>
  <div class="package-page">
    <a-row :gutter="20">
      <!-- 左侧：安装包管理 -->
      <a-col :span="12">
        <a-card title="安装包管理" class="package-card">
          <!-- 安装表单 -->
          <div class="install-section">
            <h4>安装新应用</h4>
            <a-form :model="installForm" layout="vertical">
              <a-form-item label="APK下载链接" required>
                <a-input 
                  v-model="installForm.url" 
                  placeholder="请输入APK下载链接"
                  :disabled="installing"
                />
              </a-form-item>
              <a-form-item label="包名称（可选）">
                <a-input 
                  v-model="installForm.package_name" 
                  placeholder="自动从链接提取，也可手动指定"
                  :disabled="installing"
                />
              </a-form-item>
              <a-form-item>
                <a-button 
                  type="primary" 
                  @click="installPackage"
                  :loading="installing"
                  :disabled="!installForm.url || !deviceStore.isConnected"
                  block
                >
                  <template #icon><icon-download /></template>
                  下载并安装
                </a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- 已下载的安装包列表 -->
          <div class="package-list-section">
            <div class="section-header">
              <h4>已下载的安装包</h4>
              <a-button size="small" @click="refreshPackageList" :loading="loadingPackages">
                <template #icon><icon-refresh /></template>
                刷新
              </a-button>
            </div>
            
            <a-list :data="packageList" :loading="loadingPackages">
              <template #item="{ item }">
                <a-list-item class="package-item">
                  <div class="package-info">
                    <div class="package-name">
                      <icon-file />
                      <span>{{ item.package_name }}</span>
                    </div>
                    <div class="package-meta">
                      <span>大小: {{ formatFileSize(item.file_size) }}</span>
                      <span>下载时间: {{ formatTime(item.download_time) }}</span>
                    </div>
                  </div>
                  <div class="package-actions">
                    <a-button 
                      size="small" 
                      type="outline"
                      @click="installLocalPackage(item)"
                      :disabled="!deviceStore.isConnected"
                    >
                      安装
                    </a-button>
                    <a-popconfirm 
                      content="确定要删除这个安装包吗？"
                      @ok="deletePackage(item.package_name)"
                    >
                      <a-button size="small" status="danger" type="outline">
                        删除
                      </a-button>
                    </a-popconfirm>
                  </div>
                </a-list-item>
              </template>
              
              <template #empty>
                <a-empty description="暂无已下载的安装包" />
              </template>
            </a-list>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：下载任务 -->
      <a-col :span="12">
        <a-card title="下载任务" class="task-card">
          <a-list :data="downloadTasks" :loading="loadingTasks">
            <template #item="{ item }">
              <a-list-item class="task-item">
                <div class="task-info">
                  <div class="task-header">
                    <span class="task-name">{{ item.package_name }}</span>
                    <a-tag :color="getTaskStatusColor(item.status)">
                      {{ getTaskStatusText(item.status) }}
                    </a-tag>
                  </div>
                  <div class="task-url">{{ item.url }}</div>
                  <div class="task-progress">
                    <a-progress 
                      :percent="item.progress" 
                      :status="item.status === 'failed' ? 'danger' : 'normal'"
                      :show-text="true"
                    />
                  </div>
                  <div class="task-message">{{ item.message }}</div>
                </div>
                <div class="task-actions">
                  <a-button 
                    v-if="item.status === 'pending' || item.status === 'downloading'"
                    size="small" 
                    status="danger"
                    @click="cancelTask(item.task_id)"
                  >
                    取消
                  </a-button>
                </div>
              </a-list-item>
            </template>
            
            <template #empty>
              <a-empty description="暂无下载任务" />
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/utils/websocket'
import { packageApi } from '@/api/package'
import { Message } from '@arco-design/web-vue'

const deviceStore = useDeviceStore()
const { onMessage, offMessage } = useWebSocket()

// 响应式数据
const installForm = reactive({
  url: '',
  package_name: ''
})

const packageList = ref([])
const downloadTasks = ref([])
const installing = ref(false)
const loadingPackages = ref(false)
const loadingTasks = ref(false)

// 方法
const installPackage = async () => {
  if (!deviceStore.isConnected) {
    Message.warning('请先连接设备')
    return
  }

  installing.value = true
  try {
    const response = await packageApi.installPackage(installForm)
    Message.success('安装任务已启动')
    
    // 清空表单
    installForm.url = ''
    installForm.package_name = ''
    
    // 刷新任务列表
    await refreshTaskList()
  } catch (error) {
    Message.error('启动安装任务失败: ' + error.message)
  } finally {
    installing.value = false
  }
}

const refreshPackageList = async () => {
  loadingPackages.value = true
  try {
    const response = await packageApi.getPackageList()
    packageList.value = response.data || []
  } catch (error) {
    Message.error('获取安装包列表失败: ' + error.message)
  } finally {
    loadingPackages.value = false
  }
}

const refreshTaskList = async () => {
  loadingTasks.value = true
  try {
    const response = await packageApi.getDownloadTasks()
    downloadTasks.value = response.data || []
  } catch (error) {
    Message.error('获取下载任务失败: ' + error.message)
  } finally {
    loadingTasks.value = false
  }
}

const deletePackage = async (packageName) => {
  try {
    await packageApi.deletePackage(packageName)
    Message.success('删除成功')
    await refreshPackageList()
  } catch (error) {
    Message.error('删除失败: ' + error.message)
  }
}

const cancelTask = async (taskId) => {
  try {
    await packageApi.cancelTask(taskId)
    Message.success('任务已取消')
    await refreshTaskList()
  } catch (error) {
    Message.error('取消任务失败: ' + error.message)
  }
}

const installLocalPackage = async (packageItem) => {
  // 这里可以实现本地安装包的安装逻辑
  Message.info('本地安装包安装功能待实现')
}

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const getTaskStatusColor = (status) => {
  const colors = {
    pending: 'blue',
    downloading: 'orange',
    installing: 'purple',
    completed: 'green',
    failed: 'red',
    cancelled: 'gray'
  }
  return colors[status] || 'gray'
}

const getTaskStatusText = (status) => {
  const texts = {
    pending: '等待中',
    downloading: '下载中',
    installing: '安装中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

// WebSocket消息处理
const handleProgressMessage = (message) => {
  // 更新对应任务的进度
  const task = downloadTasks.value.find(t => t.task_id === message.task_id)
  if (task) {
    task.progress = message.progress
    task.message = message.message
  }
}

// 生命周期
onMounted(async () => {
  await refreshPackageList()
  await refreshTaskList()
  
  // 注册WebSocket消息处理
  onMessage('progress', handleProgressMessage)
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  offMessage('progress')
})
</script>

<style scoped>
.package-page {
  padding: 0;
}

.package-card,
.task-card {
  margin-bottom: 20px;
}

.install-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.install-section h4 {
  margin-bottom: 16px;
}

.package-list-section h4,
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
}

.package-item,
.task-item {
  padding: 16px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  margin-bottom: 8px;
}

.package-info,
.task-info {
  flex: 1;
}

.package-name,
.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.package-name span,
.task-name {
  margin-left: 8px;
}

.package-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--color-text-3);
}

.task-url {
  font-size: 12px;
  color: var(--color-text-3);
  margin-bottom: 8px;
  word-break: break-all;
}

.task-progress {
  margin-bottom: 8px;
}

.task-message {
  font-size: 12px;
  color: var(--color-text-2);
}

.package-actions,
.task-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
