<template>
  <div class="device-page">
    <a-card title="设备管理" class="device-card">
      <!-- 设备状态显示 -->
      <div class="device-status-section">
        <a-alert 
          :type="deviceStore.isConnected ? 'success' : 'warning'"
          :message="deviceStore.isConnected ? `已连接设备: ${deviceStore.currentDeviceId}` : '未连接设备'"
          show-icon
          class="status-alert"
        />
      </div>

      <!-- 云端设备连接 -->
      <div class="cloud-device-section">
        <h3>云端设备连接</h3>
        <a-form :model="cloudDeviceForm" layout="inline">
          <a-form-item label="设备IP地址">
            <a-input
              v-model="cloudDeviceForm.ip"
              placeholder="如: *************:5555"
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              @click="connectCloudDevice"
              :loading="connectingCloud"
              :disabled="!cloudDeviceForm.ip"
            >
              连接云端设备
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 设备列表 -->
      <div class="device-list-section">
        <div class="section-header">
          <h3>可用设备</h3>
          <a-button
            type="primary"
            @click="scanDevices"
            :loading="scanning"
            size="small"
          >
            <template #icon><icon-refresh /></template>
            扫描设备
          </a-button>
        </div>

        <a-list 
          :data="deviceList" 
          :loading="scanning"
          class="device-list"
        >
          <template #item="{ item }">
            <a-list-item class="device-item">
              <div class="device-info">
                <div class="device-id">
                  <icon-mobile />
                  <span>{{ item }}</span>
                </div>
              </div>
              <div class="device-actions">
                <a-button 
                  v-if="deviceStore.currentDeviceId !== item"
                  type="primary" 
                  size="small"
                  @click="connectDevice(item)"
                  :loading="connecting && connectingDevice === item"
                >
                  连接
                </a-button>
                <a-button 
                  v-else
                  type="outline" 
                  status="success"
                  size="small"
                  @click="disconnectDevice"
                  :loading="disconnecting"
                >
                  断开连接
                </a-button>
              </div>
            </a-list-item>
          </template>
          
          <template #empty>
            <a-empty description="未发现设备，请确保设备已连接并开启USB调试" />
          </template>
        </a-list>
      </div>

      <!-- 设备详细信息 -->
      <div v-if="deviceStore.isConnected && deviceInfo" class="device-info-section">
        <div class="section-header">
          <h3>设备信息</h3>
          <a-button
            size="small"
            @click="getDeviceInfo"
            :loading="loadingDeviceInfo"
          >
            <template #icon><icon-refresh /></template>
            刷新
          </a-button>
        </div>

        <a-table
          :data="deviceInfoTableData"
          :pagination="false"
          :bordered="true"
          size="small"
          class="device-info-table"
        >
          <template #columns>
            <a-table-column title="属性" data-index="property" :width="150">
              <template #cell="{ record }">
                <span class="property-name">{{ record.property }}</span>
              </template>
            </a-table-column>
            <a-table-column title="值" data-index="value">
              <template #cell="{ record }">
                <span class="property-value">{{ record.value }}</span>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { Message } from '@arco-design/web-vue'

const deviceStore = useDeviceStore()

// 响应式数据
const deviceList = ref([])
const deviceInfo = ref(null)
const scanning = ref(false)
const connecting = ref(false)
const disconnecting = ref(false)
const connectingDevice = ref('')
const loadingDeviceInfo = ref(false)
const connectingCloud = ref(false)

// 云端设备表单
const cloudDeviceForm = reactive({
  ip: ''
})

// 计算属性
const deviceInfoTableData = computed(() => {
  if (!deviceInfo.value) return []

  return [
    { property: '设备ID', value: deviceInfo.value.device_id || '未知' },
    { property: '设备名称', value: deviceInfo.value.name || '未知' },
    { property: '设备型号', value: deviceInfo.value.model || '未知' },
    { property: '制造商', value: deviceInfo.value.manufacturer || '未知' },
    { property: 'Android版本', value: deviceInfo.value.version || '未知' },
    { property: 'API级别', value: deviceInfo.value.api_level || '未知' },
    { property: '屏幕分辨率', value: deviceInfo.value.resolution ? `${deviceInfo.value.resolution[0]} × ${deviceInfo.value.resolution[1]}` : '未知' },
    { property: '屏幕密度', value: deviceInfo.value.density || '未知' },
    { property: '屏幕尺寸', value: deviceInfo.value.screen_size ? `${deviceInfo.value.screen_size[0].toFixed(1)}" × ${deviceInfo.value.screen_size[1].toFixed(1)}"` : '未知' },
    { property: 'CPU架构', value: deviceInfo.value.cpu_abi || '未知' },
    { property: '内存信息', value: deviceInfo.value.memory || '未知' },
    { property: '存储空间', value: deviceInfo.value.storage || '未知' },
    { property: '电池电量', value: deviceInfo.value.battery ? `${deviceInfo.value.battery}%` : '未知' },
    { property: '网络状态', value: deviceInfo.value.network || '未知' },
    { property: '连接状态', value: deviceInfo.value.status === 'connected' ? '已连接' : '未连接' }
  ]
})

// 方法
const scanDevices = async () => {
  scanning.value = true
  try {
    const devices = await deviceStore.scanDevices()
    console.log('从store获取的设备列表:', devices, typeof devices, Array.isArray(devices))

    // 确保devices是数组
    const deviceArray = Array.isArray(devices) ? devices : []
    deviceList.value = deviceArray

    if (deviceArray.length === 0) {
      Message.warning('未发现设备，请检查设备连接和USB调试设置')
    } else {
      Message.success(`发现 ${deviceArray.length} 个设备`)
    }
  } catch (error) {
    console.error('扫描设备错误:', error)
    Message.error('扫描设备失败: ' + (error.message || '未知错误'))
    deviceList.value = []
  } finally {
    scanning.value = false
  }
}

const connectDevice = async (deviceId) => {
  connecting.value = true
  connectingDevice.value = deviceId
  try {
    await deviceStore.connectDevice(deviceId)
    await getDeviceInfo()
    Message.success(`设备 ${deviceId} 连接成功`)
  } catch (error) {
    Message.error('连接设备失败: ' + error.message)
  } finally {
    connecting.value = false
    connectingDevice.value = ''
  }
}

const disconnectDevice = async () => {
  disconnecting.value = true
  try {
    await deviceStore.disconnectDevice()
    deviceInfo.value = null
    Message.success('设备连接已断开')
  } catch (error) {
    Message.error('断开连接失败: ' + error.message)
  } finally {
    disconnecting.value = false
  }
}

const connectCloudDevice = async () => {
  connectingCloud.value = true
  try {
    // 首先尝试连接到云端设备
    await deviceStore.connectCloudDevice(cloudDeviceForm.ip)
    await getDeviceInfo()
    Message.success(`云端设备 ${cloudDeviceForm.ip} 连接成功`)
    cloudDeviceForm.ip = '' // 清空输入框
  } catch (error) {
    Message.error('连接云端设备失败: ' + error.message)
  } finally {
    connectingCloud.value = false
  }
}

const getDeviceInfo = async () => {
  loadingDeviceInfo.value = true
  try {
    deviceInfo.value = await deviceStore.getDeviceInfo()
    Message.success('设备信息已更新')
  } catch (error) {
    console.error('获取设备信息失败:', error)
    Message.error('获取设备信息失败: ' + error.message)
  } finally {
    loadingDeviceInfo.value = false
  }
}

// 生命周期
onMounted(async () => {
  await scanDevices()
  if (deviceStore.isConnected) {
    await getDeviceInfo()
  }
})
</script>

<style scoped>
.device-page {
  min-height: 100vh;
  padding: 20px;
}

.device-card {
  height: auto;
  min-height: 600px;
}

.device-status-section {
  margin-bottom: 20px;
}

.cloud-device-section {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-fill-1);
  border-radius: 6px;
  border: 1px solid var(--color-border);
}

.cloud-device-section h3 {
  margin-bottom: 16px;
}

.device-list-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
}

.device-list {
  border: 1px solid var(--color-border);
  border-radius: 6px;
}

.device-item {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  flex: 1;
}

.device-id {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.device-id span {
  margin-left: 8px;
}

.device-actions {
  display: flex;
  gap: 8px;
}

.device-info-section {
  border-top: 1px solid var(--color-border);
  padding-top: 20px;
}

.device-info-section h3 {
  margin-bottom: 16px;
}

.device-info-table {
  margin-top: 16px;
}

.property-name {
  font-weight: 500;
  color: var(--color-text-1);
}

.property-value {
  color: var(--color-text-2);
  font-family: monospace;
}
</style>
