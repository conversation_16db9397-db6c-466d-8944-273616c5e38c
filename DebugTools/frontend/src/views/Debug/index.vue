<template>
  <div class="debug-page">
    <a-row :gutter="20">
      <!-- 左侧：ADB调试 -->
      <a-col :span="12">
        <a-card title="ADB命令调试" class="debug-card">
          <!-- ADB命令输入 -->
          <div class="command-input-section">
            <a-input-group compact>
              <a-input
                addon-before="adb shell"
                v-model="adbCommand"
                placeholder="输入shell命令，如: getprop ro.build.version.sdk"
                @keyup.enter="executeAdbCommand"
                :disabled="!deviceStore.isConnected"
                style="width: calc(100% - 80px)"
              />
              <a-button 
                type="primary"
                @click="executeAdbCommand"
                :loading="adbLoading"
                :disabled="!deviceStore.isConnected || !adbCommand.trim()"
                style="width: 80px"
              >
                执行
              </a-button>
            </a-input-group>
          </div>

          <!-- ADB命令结果 -->
          <div class="command-result-section">
            <div class="result-header">
              <h4>执行结果</h4>
              <a-button size="small" @click="clearAdbResult">
                <template #icon><icon-delete /></template>
                清空
              </a-button>
            </div>
            <div class="result-container">
              <pre class="result-text">{{ adbResult || '暂无执行结果' }}</pre>
            </div>
          </div>

          <!-- ADB历史记录 -->
          <div class="command-history-section">
            <div class="history-header">
              <h4>历史记录</h4>
              <a-button size="small" @click="refreshAdbHistory" :loading="loadingAdbHistory">
                <template #icon><icon-refresh /></template>
                刷新
              </a-button>
            </div>
            <a-list :data="adbHistory" :loading="loadingAdbHistory" size="small">
              <template #item="{ item }">
                <a-list-item class="history-item">
                  <div class="history-content" @click="selectAdbCommand(item.command)">
                    <div class="history-command">
                      <icon-code />
                      <span>{{ item.command }}</span>
                    </div>
                    <div class="history-time">{{ formatTime(item.timestamp) }}</div>
                  </div>
                </a-list-item>
              </template>
              
              <template #empty>
                <a-empty description="暂无历史记录" />
              </template>
            </a-list>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：Poco调试 -->
      <a-col :span="12">
        <a-card title="Poco语句调试" class="debug-card">
          <!-- Poco命令输入 -->
          <div class="command-input-section">
            <a-textarea
              v-model="pocoQuery"
              placeholder="输入Poco查询语句，如: ('com.example:id/button').exists()"
              :rows="3"
              :disabled="!deviceStore.isConnected"
            />
            <a-button 
              type="primary"
              @click="executePocoQuery"
              :loading="pocoLoading"
              :disabled="!deviceStore.isConnected || !pocoQuery.trim()"
              block
              style="margin-top: 8px"
            >
              执行Poco查询
            </a-button>
          </div>

          <!-- Poco查询结果 -->
          <div class="command-result-section">
            <div class="result-header">
              <h4>执行结果</h4>
              <a-button size="small" @click="clearPocoResult">
                <template #icon><icon-delete /></template>
                清空
              </a-button>
            </div>
            <div class="result-container">
              <pre class="result-text">{{ pocoResult || '暂无执行结果' }}</pre>
            </div>
          </div>

          <!-- Poco历史记录 -->
          <div class="command-history-section">
            <div class="history-header">
              <h4>历史记录</h4>
              <a-button size="small" @click="refreshPocoHistory" :loading="loadingPocoHistory">
                <template #icon><icon-refresh /></template>
                刷新
              </a-button>
            </div>
            <a-list :data="pocoHistory" :loading="loadingPocoHistory" size="small">
              <template #item="{ item }">
                <a-list-item class="history-item">
                  <div class="history-content" @click="selectPocoQuery(item.command)">
                    <div class="history-command">
                      <icon-code />
                      <span>{{ item.command }}</span>
                    </div>
                    <div class="history-time">{{ formatTime(item.timestamp) }}</div>
                  </div>
                </a-list-item>
              </template>
              
              <template #empty>
                <a-empty description="暂无历史记录" />
              </template>
            </a-list>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 设备日志区域 -->
    <a-row style="margin-top: 20px">
      <a-col :span="24">
        <a-card title="设备日志" class="log-card">
          <div class="log-actions">
            <a-button 
              type="primary"
              @click="getDeviceLogs"
              :loading="logLoading"
              :disabled="!deviceStore.isConnected"
            >
              <template #icon><icon-file-text /></template>
              获取日志
            </a-button>
            <a-button 
              @click="clearDeviceLogs"
              :disabled="!deviceStore.isConnected"
            >
              <template #icon><icon-delete /></template>
              清空设备日志
            </a-button>
            <a-button size="small" @click="clearDisplayLogs">
              清空显示
            </a-button>
          </div>
          
          <div class="log-container">
            <pre class="log-text">{{ deviceLogs || '暂无日志内容，点击"获取日志"按钮获取设备日志' }}</pre>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { debugApi } from '@/api/debug'
import { Message } from '@arco-design/web-vue'

const deviceStore = useDeviceStore()

// 响应式数据
const adbCommand = ref('')
const adbResult = ref('')
const adbHistory = ref([])
const pocoQuery = ref('')
const pocoResult = ref('')
const pocoHistory = ref([])
const deviceLogs = ref('')

// 加载状态
const adbLoading = ref(false)
const pocoLoading = ref(false)
const logLoading = ref(false)
const loadingAdbHistory = ref(false)
const loadingPocoHistory = ref(false)

// 方法
const executeAdbCommand = async () => {
  if (!adbCommand.value.trim()) return
  
  adbLoading.value = true
  try {
    const response = await debugApi.executeAdbCommand({ command: adbCommand.value })
    adbResult.value = response.data.result
    
    if (response.data.success) {
      Message.success('ADB命令执行成功')
    } else {
      Message.warning('ADB命令执行完成，但可能有错误')
    }
    
    // 刷新历史记录
    await refreshAdbHistory()
  } catch (error) {
    Message.error('ADB命令执行失败: ' + error.message)
  } finally {
    adbLoading.value = false
  }
}

const executePocoQuery = async () => {
  if (!pocoQuery.value.trim()) return
  
  pocoLoading.value = true
  try {
    const response = await debugApi.executePocoQuery({ query: pocoQuery.value })
    pocoResult.value = response.data.result
    
    if (response.data.success) {
      Message.success('Poco查询执行成功')
    } else {
      Message.warning('Poco查询执行完成，但可能有错误')
    }
    
    // 刷新历史记录
    await refreshPocoHistory()
  } catch (error) {
    Message.error('Poco查询执行失败: ' + error.message)
  } finally {
    pocoLoading.value = false
  }
}

const getDeviceLogs = async () => {
  logLoading.value = true
  try {
    const response = await debugApi.getDeviceLogs()
    deviceLogs.value = response.data.data.logs
    Message.success('获取设备日志成功')
  } catch (error) {
    Message.error('获取设备日志失败: ' + error.message)
  } finally {
    logLoading.value = false
  }
}

const clearDeviceLogs = async () => {
  try {
    await debugApi.clearDeviceLogs()
    Message.success('设备日志已清空')
  } catch (error) {
    Message.error('清空设备日志失败: ' + error.message)
  }
}

const refreshAdbHistory = async () => {
  loadingAdbHistory.value = true
  try {
    const response = await debugApi.getAdbHistory()
    adbHistory.value = response.data || []
  } catch (error) {
    console.error('获取ADB历史记录失败:', error)
  } finally {
    loadingAdbHistory.value = false
  }
}

const refreshPocoHistory = async () => {
  loadingPocoHistory.value = true
  try {
    const response = await debugApi.getPocoHistory()
    pocoHistory.value = response.data || []
  } catch (error) {
    console.error('获取Poco历史记录失败:', error)
  } finally {
    loadingPocoHistory.value = false
  }
}

const selectAdbCommand = (command) => {
  adbCommand.value = command
}

const selectPocoQuery = (query) => {
  pocoQuery.value = query
}

const clearAdbResult = () => {
  adbResult.value = ''
}

const clearPocoResult = () => {
  pocoResult.value = ''
}

const clearDisplayLogs = () => {
  deviceLogs.value = ''
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

// 生命周期
onMounted(async () => {
  await refreshAdbHistory()
  await refreshPocoHistory()
})
</script>

<style scoped>
.debug-page {
  padding: 0;
}

.debug-card,
.log-card {
  margin-bottom: 20px;
}

.command-input-section {
  margin-bottom: 20px;
}

.command-result-section {
  margin-bottom: 20px;
}

.result-header,
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-header h4,
.history-header h4 {
  margin: 0;
}

.result-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-fill-1);
  min-height: 100px;
  overflow: auto;
}

.result-text {
  padding: 12px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.command-history-section {
  overflow: auto;
}

.history-item {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: var(--color-fill-1);
}

.history-content {
  width: 100%;
}

.history-command {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.history-command span {
  margin-left: 8px;
}

.history-time {
  font-size: 11px;
  color: var(--color-text-3);
}

.log-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.log-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-fill-1);
  min-height: 200px;
  overflow: auto;
}

.log-text {
  padding: 12px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.3;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
