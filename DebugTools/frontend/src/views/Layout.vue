<template>
  <a-layout class="layout">
    <!-- 顶部导航 -->
    <a-layout-header class="header">
      <div class="header-content">
        <div class="logo">
          <icon-mobile />
          <span>DebugTools</span>
        </div>
        
        <!-- 设备状态 -->
        <div class="device-status">
          <a-badge :status="deviceStore.isConnected ? 'processing' : 'default'">
            <span>{{ deviceStore.isConnected ? `已连接: ${deviceStore.currentDeviceId}` : '未连接设备' }}</span>
          </a-badge>
        </div>
        
        <!-- 用户操作 -->
        <div class="user-actions">
          <a-button type="text" @click="toggleTheme">
            <icon-sun v-if="isDark" />
            <icon-moon v-else />
          </a-button>
        </div>
      </div>
    </a-layout-header>

    <a-layout>
      <!-- 侧边栏 -->
      <a-layout-sider 
        :width="200" 
        :collapsed="collapsed" 
        :collapsible="true"
        @collapse="onCollapse"
        class="sider"
      >
        <a-menu 
          :selected-keys="[currentRoute]"
          :default-open-keys="['android']"
          mode="vertical"
          @menu-item-click="onMenuClick"
        >
          <a-sub-menu key="android">
            <template #icon><icon-mobile /></template>
            <template #title>Android调试</template>
            
            <a-menu-item key="/device">
              <template #icon><icon-settings /></template>
              设备管理
            </a-menu-item>
            
            <a-menu-item key="/package">
              <template #icon><icon-apps /></template>
              安装包管理
            </a-menu-item>
            
            <a-menu-item key="/operation">
              <template #icon><icon-interaction /></template>
              业务操作
            </a-menu-item>
            
            <a-menu-item key="/debug">
              <template #icon><icon-bug /></template>
              调试工具
            </a-menu-item>
          </a-sub-menu>
          
          <a-menu-item key="/logs">
            <template #icon><icon-file-text /></template>
            日志管理
          </a-menu-item>
        </a-menu>
      </a-layout-sider>

      <!-- 主内容区 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/utils/websocket'

const router = useRouter()
const route = useRoute()
const deviceStore = useDeviceStore()

const collapsed = ref(false)
const isDark = ref(false)

const currentRoute = computed(() => route.path)

// WebSocket连接
const { connect } = useWebSocket()

const onCollapse = (val) => {
  collapsed.value = val
}

const onMenuClick = (key) => {
  router.push(key)
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.body.setAttribute('arco-theme', isDark.value ? 'dark' : '')
}

onMounted(() => {
  // 连接WebSocket
  connect()
  
  // 初始化设备状态
  deviceStore.fetchDeviceStatus()
})
</script>

<style scoped>
.layout {
  height: 100vh;
}

.header {
  background: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--color-text-1);
}

.logo span {
  margin-left: 8px;
}

.device-status {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-actions {
  display: flex;
  align-items: center;
}

.sider {
  background: var(--color-bg-2);
  border-right: 1px solid var(--color-border);
}

.content {
  background: var(--color-bg-1);
  padding: 20px;
}

.content-wrapper {
  height: 100%;
  background: var(--color-bg-2);
  border-radius: 6px;
  padding: 20px;
}
</style>
