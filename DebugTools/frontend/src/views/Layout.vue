<template>
  <div class="layout">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo">
          <icon-mobile />
          <span>DebugTools</span>
        </div>

        <!-- 顶部菜单 -->
        <div class="top-menu">
          <a-menu
            :selected-keys="[currentRoute]"
            mode="horizontal"
            @menu-item-click="onMenuClick"
            class="main-menu"
          >
            <a-menu-item key="/device">
              <template #icon><icon-settings /></template>
              设备管理
            </a-menu-item>

            <a-menu-item key="/package">
              <template #icon><icon-apps /></template>
              安装包管理
            </a-menu-item>

            <a-menu-item key="/operation">
              <template #icon><icon-interaction /></template>
              业务操作
            </a-menu-item>

            <a-menu-item key="/debug">
              <template #icon><icon-bug /></template>
              调试工具
            </a-menu-item>

            <a-menu-item key="/logs">
              <template #icon><icon-file-text /></template>
              日志管理
            </a-menu-item>
          </a-menu>
        </div>

        <!-- 右侧状态和操作 -->
        <div class="header-right">
          <!-- 设备状态 -->
          <div class="device-status">
            <a-badge :status="deviceStore.isConnected ? 'processing' : 'default'">
              <span>{{ deviceStore.isConnected ? `已连接: ${deviceStore.currentDeviceId}` : '未连接设备' }}</span>
            </a-badge>
          </div>

          <!-- 用户操作 -->
          <div class="user-actions">
            <a-button type="text" @click="toggleTheme">
              <icon-sun v-if="isDark" />
              <icon-moon v-else />
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/utils/websocket'

const router = useRouter()
const route = useRoute()
const deviceStore = useDeviceStore()

const isDark = ref(false)

const currentRoute = computed(() => route.path)

// WebSocket连接
const { connect } = useWebSocket()

const onMenuClick = (key) => {
  router.push(key)
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.body.setAttribute('arco-theme', isDark.value ? 'dark' : '')
}

onMounted(() => {
  // 连接WebSocket
  connect()

  // 初始化设备状态
  deviceStore.fetchDeviceStatus()
})
</script>

<style scoped>
.layout {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
  padding: 0 20px;
  height: 64px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--color-text-1);
  flex-shrink: 0;
}

.logo span {
  margin-left: 8px;
}

.top-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 20px;
}

.main-menu {
  background: transparent;
  border-bottom: none;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-shrink: 0;
}

.device-status {
  display: flex;
  align-items: center;
}

.user-actions {
  display: flex;
  align-items: center;
}

.content {
  flex: 1;
  background: var(--color-bg-1);
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px 0;
  }

  .top-menu {
    margin: 10px 0;
    width: 100%;
  }

  .main-menu {
    justify-content: center;
  }

  .header-right {
    gap: 10px;
  }

  .content {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .logo span {
    display: none;
  }

  .device-status span {
    font-size: 12px;
  }
}
</style>
