<template>
  <div class="logs-page">
    <a-card title="日志管理" class="logs-card">
      <!-- 日志过滤器 -->
      <div class="log-filters">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select 
              v-model="filters.level" 
              placeholder="选择日志级别"
              allow-clear
              @change="refreshLogs"
            >
              <a-option value="">全部级别</a-option>
              <a-option value="DEBUG">DEBUG</a-option>
              <a-option value="INFO">INFO</a-option>
              <a-option value="WARNING">WARNING</a-option>
              <a-option value="ERROR">ERROR</a-option>
              <a-option value="CRITICAL">CRITICAL</a-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select 
              v-model="filters.module" 
              placeholder="选择模块"
              allow-clear
              @change="refreshLogs"
            >
              <a-option value="">全部模块</a-option>
              <a-option v-for="module in modules" :key="module" :value="module">
                {{ module }}
              </a-option>
            </a-select>
          </a-col>
          <a-col :span="8">
            <a-input-search
              v-model="filters.keyword"
              placeholder="搜索日志内容"
              @search="refreshLogs"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-button-group>
              <a-button @click="refreshLogs" :loading="loading">
                <template #icon><icon-refresh /></template>
                刷新
              </a-button>
              <a-button @click="clearLogs">
                <template #icon><icon-delete /></template>
                清空
              </a-button>
            </a-button-group>
          </a-col>
        </a-row>
      </div>

      <!-- 日志统计信息 -->
      <div class="log-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总日志数" :value="stats.total_logs" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="错误数" :value="stats.level_counts?.ERROR || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="警告数" :value="stats.level_counts?.WARNING || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="文件大小" :value="stats.file_size_formatted || formatFileSize(stats.file_size || 0)" />
          </a-col>
        </a-row>
      </div>

      <!-- 日志列表 -->
      <div class="log-list">
        <a-table 
          :data="logs" 
          :loading="loading"
          :pagination="pagination"
          @page-change="onPageChange"
          @page-size-change="onPageSizeChange"
          size="small"
          :scroll="{ y: 400 }"
        >
          <template #columns>
            <a-table-column 
              title="时间" 
              data-index="timestamp" 
              :width="180"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
            >
              <template #cell="{ record }">
                <span class="timestamp">{{ formatTime(record.timestamp) }}</span>
              </template>
            </a-table-column>
            
            <a-table-column title="级别" data-index="level" :width="80">
              <template #cell="{ record }">
                <a-tag :color="getLevelColor(record.level)" size="small">
                  {{ record.level }}
                </a-tag>
              </template>
            </a-table-column>
            
            <a-table-column title="模块" data-index="module" :width="120">
              <template #cell="{ record }">
                <span class="module">{{ record.module }}</span>
              </template>
            </a-table-column>
            
            <a-table-column title="函数" data-index="function" :width="120">
              <template #cell="{ record }">
                <span class="function">{{ record.function }}:{{ record.line }}</span>
              </template>
            </a-table-column>
            
            <a-table-column title="消息" data-index="message">
              <template #cell="{ record }">
                <div class="message" :title="record.message">
                  {{ record.message }}
                </div>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <!-- 实时日志开关 -->
      <div class="real-time-logs">
        <a-switch 
          v-model="realTimeEnabled"
          @change="toggleRealTime"
        >
          <template #checked>实时日志已开启</template>
          <template #unchecked>实时日志已关闭</template>
        </a-switch>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/utils/websocket'
import { logApi } from '@/api/log'
import { Message, Modal } from '@arco-design/web-vue'

const { onMessage, offMessage } = useWebSocket()

// 响应式数据
const logs = ref([])
const modules = ref([])
const stats = ref({})
const loading = ref(false)
const realTimeEnabled = ref(false)

const filters = reactive({
  level: '',
  module: '',
  keyword: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['20', '50', '100', '200']
})

// 方法
const refreshLogs = async () => {
  loading.value = true
  try {
    const params = {
      level: filters.level || undefined,
      module: filters.module || undefined,
      keyword: filters.keyword || undefined,
      limit: pagination.pageSize,
      offset: (pagination.current - 1) * pagination.pageSize
    }
    
    const response = await logApi.getLogs(params)
    const result = response.data || {}
    logs.value = result.logs || []

    // 更新分页信息
    pagination.total = result.total || 0
  } catch (error) {
    Message.error('获取日志失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const getModules = async () => {
  try {
    const response = await logApi.getModules()
    modules.value = response.data?.modules || []
  } catch (error) {
    console.error('获取模块列表失败:', error)
  }
}

const getStats = async () => {
  try {
    const response = await logApi.getStats()
    stats.value = response.data || {}
  } catch (error) {
    console.error('获取日志统计失败:', error)
  }
}

const clearLogs = () => {
  Modal.confirm({
    title: '确认清空日志',
    content: '此操作将清空所有日志记录，是否继续？',
    onOk: async () => {
      try {
        await logApi.clearLogs()
        Message.success('日志已清空')
        await refreshLogs()
        await getStats()
      } catch (error) {
        Message.error('清空日志失败: ' + error.message)
      }
    }
  })
}

const toggleRealTime = (enabled) => {
  if (enabled) {
    // 注册实时日志消息处理
    onMessage('log', handleRealTimeLog)
    Message.success('实时日志已开启')
  } else {
    // 移除实时日志消息处理
    offMessage('log')
    Message.info('实时日志已关闭')
  }
}

const handleRealTimeLog = (logData) => {
  // 将新日志添加到列表顶部
  logs.value.unshift(logData)
  
  // 限制显示的日志数量，避免内存占用过多
  if (logs.value.length > 1000) {
    logs.value = logs.value.slice(0, 1000)
  }
}

const onPageChange = (page) => {
  pagination.current = page
  refreshLogs()
}

const onPageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  refreshLogs()
}

// 工具函数
const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getLevelColor = (level) => {
  const colors = {
    DEBUG: 'gray',
    INFO: 'blue',
    WARNING: 'orange',
    ERROR: 'red',
    CRITICAL: 'purple'
  }
  return colors[level] || 'gray'
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    refreshLogs(),
    getModules(),
    getStats()
  ])
})

onUnmounted(() => {
  // 清理WebSocket监听
  if (realTimeEnabled.value) {
    offMessage('log')
  }
})
</script>

<style scoped>
.logs-page {
  padding: 0;
}

.logs-card {
  margin-bottom: 20px;
}

.log-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-fill-1);
  border-radius: 6px;
}

.log-stats {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--color-fill-1);
  border-radius: 6px;
}

.log-list {
  margin-bottom: 20px;
}

.timestamp {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.module {
  font-weight: 500;
  color: var(--color-text-2);
}

.function {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--color-text-3);
}

.message {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.message:hover {
  overflow: visible;
  white-space: normal;
  word-break: break-all;
}

.real-time-logs {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid var(--color-border);
}
</style>
