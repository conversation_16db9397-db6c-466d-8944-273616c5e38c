import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ArcoVue from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import { createI18n } from 'vue-i18n'
import router from './router'
import App from './App.vue'

// 样式导入
import '@arco-design/web-vue/dist/arco.css'
import './styles/main.css'

// 国际化配置
const i18n = createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en',
  messages: {
    'zh-CN': {
      // 中文翻译
    },
    'en': {
      // 英文翻译
    }
  }
})

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ArcoVue)
app.use(ArcoVueIcon)
app.use(i18n)

// 全局配置
app.config.globalProperties.$ELEMENT_SIZE = 'default'

app.mount('#app')
