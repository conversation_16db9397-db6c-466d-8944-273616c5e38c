hoistPattern:
  - '*'
hoistedDependencies:
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@intlify/core-base@9.14.4':
    '@intlify/core-base': private
  '@intlify/message-compiler@9.14.4':
    '@intlify/message-compiler': private
  '@intlify/shared@9.14.4':
    '@intlify/shared': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@one-ini/wasm@0.1.1':
    '@one-ini/wasm': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rollup/pluginutils@5.1.4(rollup@4.42.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.42.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.42.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.42.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.42.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.42.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.42.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.42.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.42.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.42.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.42.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.42.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.42.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.42.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.42.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.42.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.42.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.42.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.42.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.42.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.42.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vant/popperjs@1.3.0':
    '@vant/popperjs': private
  '@vant/use@1.6.0(vue@3.5.16(typescript@5.8.3))':
    '@vant/use': private
  '@vitest/expect@1.6.1':
    '@vitest/expect': private
  '@vitest/runner@1.6.1':
    '@vitest/runner': private
  '@vitest/snapshot@1.6.1':
    '@vitest/snapshot': private
  '@vitest/spy@1.6.1':
    '@vitest/spy': private
  '@vitest/utils@1.6.1':
    '@vitest/utils': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  abbrev@2.0.0:
    abbrev: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  assertion-error@1.1.0:
    assertion-error: private
  asynckit@0.4.0:
    asynckit: private
  b-tween@0.3.3:
    b-tween: private
  b-validate@1.5.3:
    b-validate: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  chai@4.5.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  check-error@1.0.3:
    check-error: private
  chokidar@3.6.0:
    chokidar: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@10.0.1:
    commander: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  config-chain@1.1.13:
    config-chain: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  deep-eql@4.1.4:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  editorconfig@1.0.4:
    editorconfig: private
  emoji-regex@8.0.0:
    emoji-regex: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.21.5:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  execa@8.0.1:
    execa: private
  exsolve@1.0.5:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.3:
    form-data: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-func-name@2.0.2:
    get-func-name: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  human-signals@5.0.0:
    human-signals: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-stream@3.0.0:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  js-beautify@1.15.4:
    js-beautify: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loupe@2.3.7:
    loupe: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.4:
    mlly: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  nopt@7.2.1:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  number-precision@1.6.0:
    number-precision: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-limit@5.0.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@1.1.1:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss@8.5.4:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@29.7.0:
    pretty-format: private
  proto-list@1.2.4:
    proto-list: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  readdirp@3.6.0:
    readdirp: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.42.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  scule@1.3.0:
    scule: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.1:
    strip-literal: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  text-table@0.2.0:
    text-table: private
  tinybench@2.9.0:
    tinybench: private
  tinypool@0.8.4:
    tinypool: private
  tinyspy@2.2.1:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.1.0:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  unimport@3.14.6(rollup@4.42.0):
    unimport: private
  unplugin@1.16.1:
    unplugin: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vite-node@1.6.1:
    vite-node: private
  vue-component-type-helpers@2.2.10:
    vue-component-type-helpers: private
  vue-demi@0.14.10(vue@3.5.16(typescript@5.8.3)):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@8.57.1):
    vue-eslint-parser: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yocto-queue@1.2.1:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.2.1
pendingBuilds: []
prunedAt: Tue, 10 Jun 2025 12:13:32 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.42.0'
  - '@rollup/rollup-android-arm64@4.42.0'
  - '@rollup/rollup-darwin-x64@4.42.0'
  - '@rollup/rollup-freebsd-arm64@4.42.0'
  - '@rollup/rollup-freebsd-x64@4.42.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.42.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.42.0'
  - '@rollup/rollup-linux-arm64-gnu@4.42.0'
  - '@rollup/rollup-linux-arm64-musl@4.42.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.42.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.42.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.42.0'
  - '@rollup/rollup-linux-riscv64-musl@4.42.0'
  - '@rollup/rollup-linux-s390x-gnu@4.42.0'
  - '@rollup/rollup-linux-x64-gnu@4.42.0'
  - '@rollup/rollup-linux-x64-musl@4.42.0'
  - '@rollup/rollup-win32-arm64-msvc@4.42.0'
  - '@rollup/rollup-win32-ia32-msvc@4.42.0'
  - '@rollup/rollup-win32-x64-msvc@4.42.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
