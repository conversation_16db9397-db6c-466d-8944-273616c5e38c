{"name": "debugtools-frontend", "version": "1.0.0", "description": "DebugTools前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@arco-design/web-vue": "^2.54.0", "@arco-design/color": "^0.4.0", "vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "vue-i18n": "^9.8.0", "vant": "^4.8.0", "@vant/touch-emulator": "^1.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/test-utils": "^2.4.2", "vite": "^5.0.0", "vitest": "^1.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}}