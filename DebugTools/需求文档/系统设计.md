## 项目介绍
当前项目主要为一个前后端结构的项目，主要实现android、web、ios、harmony等设备的调试工具集合，并提供一个web端的管理后台，方便用户进行设备的管理和调试。

## 项目技术栈
- 前端
    - 使用vue3+vite+pinia+arco-design+axios+vant等技术栈
    - 使用字节开源的arco.design作为组件库（https://arco.design/）
    - 使用axios作为请求库
    - 使用pinia作为状态管理
    - 使用vite作为构建工具
    - 使用vue-router作为路由管理
    - 使用vue-i18n作为国际化管理
    - 使用vue-i18n作为国际化管理
- 后端
    - 使用python3.11+fastapi+uvicorn+sqlalchemy等技术栈
    - 使用mysql作为数据库
    - 使用fastapi作为web框架
    - 使用uvicorn作为wsgi服务器
    - 使用langchain作为大模型调用库
    - 使用loguru作为日志管理
    - 使用pytest作为测试框架

## 核心要求
1. 需要将设备截图、点击、输入、滚动、断言等业务操作抽象出来，方便后续可以替换底层实现，例如android点击操作，底层可以使用airtest+poco，后续也可以直接替换底层实现为appium，对应的业务操作方法调用不变。
2. 每次完成一个功能后需要维护一份mermaid流程图等，后续更新的话也需要更新流程图。
3. 前端页面需要在顶部有一级、二级菜单。
4. 对应的前端和服务端需要创建一个tests测试目录，后续如果创建了测试文件需要放在这个目录下。
5. 服务端需要使用conda的虚拟环境：debugtools-py311，并使用requirements.txt文件进行依赖管理。
6. 服务端必须要使用loguru作为日志管理，不能使用print打印日志。

## 核心功能（根据后续的详细需求文件再去实现，当前只是这个项目框架的大致功能）
1. android端调试工具
2. web端调试工具
3. ios端调试工具
4. harmony端调试工具
5. 大模型调用
6. 管理后台
7. 日志管理